"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { User, Save, Loader2, X } from "lucide-react";
import { addCustomer } from "@/actions/entities/customers";

// Simplified customer schema for the modal (based on the supplier modal pattern)
const customerModalSchema = z.object({
  name: z.string().min(1, "<PERSON>a pelanggan wajib diisi"),
  contactName: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  phone: z.string().min(1, "Nomor telepon wajib diisi"),
  address: z.string().optional(),
  notes: z.string().optional(),
});

type CustomerModalFormData = z.infer<typeof customerModalSchema>;

interface CustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCustomerCreated: (customer: {
    id: string;
    name: string;
    email?: string;
  }) => void;
}

export const CustomerModal: React.FC<CustomerModalProps> = ({
  isOpen,
  onClose,
  onCustomerCreated,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CustomerModalFormData>({
    resolver: zodResolver(customerModalSchema),
    defaultValues: {
      name: "",
      contactName: "",
      email: "",
      phone: "",
      address: "",
      notes: "",
    },
  });

  const onSubmit = async (
    data: CustomerModalFormData,
    event?: React.FormEvent
  ) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    setIsLoading(true);
    try {
      // Convert the modal form data to the full customer schema format
      const customerData = {
        name: data.name,
        firstName: "",
        middleName: "",
        lastName: "",
        contactName: data.contactName || "",
        phone: data.phone || "",
        telephone: "",
        fax: "",
        email: data.email || "",
        identityType: "",
        identityNumber: "",
        NIK: "",
        NPWP: "",
        companyName: "",
        otherInfo: "",
        address: data.address || "",
        billingAddress: "",
        shippingAddress: "",
        sameAsShipping: false,
        bankName: "",
        bankBranch: "",
        accountHolder: "",
        accountNumber: "",
        notes: data.notes || "",
      };

      const result = await addCustomer(customerData);

      if (result.success && result.customer) {
        toast.success("Pelanggan berhasil ditambahkan!");

        // Call the callback with the new customer data including email
        onCustomerCreated({
          id: result.customer.id,
          name: result.customer.name,
          email: result.customer.email || "",
        });

        // Reset form and close modal
        form.reset();
        onClose();
      } else {
        toast.error(result.error || "Gagal menambahkan pelanggan");
      }
    } catch (error: any) {
      console.error("Error creating customer:", error);

      // Check if it's a 504 Gateway Timeout error
      if (
        error?.status === 504 ||
        error?.message?.includes("504") ||
        error?.name === "TimeoutError"
      ) {
        // For 504 errors, the customer might have been created successfully
        // Show a different message and suggest checking the customer list
        toast.warning(
          "Koneksi timeout. Pelanggan mungkin sudah berhasil ditambahkan. Silakan periksa daftar pelanggan dan refresh halaman jika diperlukan.",
          { duration: 8000 }
        );

        // Still close the modal and reset form as the operation might have succeeded
        form.reset();
        onClose();

        // Trigger a refresh of the customers list if callback is available
        // For other errors, show the standard error message
        toast.error("Terjadi kesalahan saat menambahkan pelanggan");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Tambah Pelanggan Baru
          </DialogTitle>
          <DialogDescription>
            Buat pelanggan baru untuk ditambahkan ke penjualan ini. Pelanggan akan
            tersimpan dan dapat digunakan untuk penjualan selanjutnya.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit((data) => onSubmit(data, e))(e);
            }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Company/Customer Name */}
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Nama Perusahaan/Pelanggan{" "}
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Masukkan nama perusahaan atau pelanggan"
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Contact Name */}
              <FormField
                control={form.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Kontak</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Nama kontak person"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="<EMAIL>"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Phone */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nomor Telepon <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="08xxxxxxxxxx"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Address */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Alamat</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Alamat lengkap pelanggan"
                      rows={3}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Catatan tambahan tentang pelanggan"
                      rows={2}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-col sm:flex-row gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleClose();
                }}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                <X className="h-4 w-4 mr-2" />
                Batal
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Simpan Pelanggan
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
